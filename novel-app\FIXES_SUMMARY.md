# 修复总结

本次修复解决了用户提出的三个主要问题：

## 1. novelId 需要和书名相关，不能随机生成

### 问题描述
- 原来的 `generateId()` 函数使用时间戳+随机数生成ID
- 导致重新解析小说时ID会变化，之前添加的人物等数据会失效

### 解决方案
- 在 `src/lib/database.ts` 中添加了 `generateDeterministicId()` 函数
- 使用 MD5 哈希基于书名生成确定性ID
- 修改 `novelDb.create()` 方法，使用书名生成固定ID
- 如果相同书名的小说已存在，会更新现有记录而不是创建新记录

### 修改文件
- `src/lib/database.ts`

## 2. 改写完成没有结束状态，一直是改写中

### 问题描述
- 改写任务完成后状态仍显示为 "processing" 或 "failed"
- 错误信息："Cannot read properties of null (reading 'success')"
- 这是由于处理章节结果时存在 null 值导致的

### 解决方案
- 在 `src/app/api/rewrite/route.ts` 中增加了更严格的 null 检查
- 修复了章节完成回调中的 null 值处理
- 改进了最终状态更新逻辑，确保任务正确标记为 'completed'
- 修复了结果统计逻辑，过滤掉 null 值

### 修改文件
- `src/app/api/rewrite/route.ts`

## 3. 增加合并功能，将生成的章节合并为一个文件

### 问题描述
- 改写完成的章节分别保存为独立文件
- 缺少将所有章节合并为完整小说文件的功能

### 解决方案
- 在 `src/lib/file-manager.ts` 中添加了合并相关方法：
  - `getDoneNovelsDir()`: 获取完成小说目录
  - `mergeRewrittenChapters()`: 合并改写章节为完整文件
- 创建了新的 API 端点 `src/app/api/merge/route.ts`：
  - POST: 合并指定小说的章节
  - GET: 获取可合并的小说列表
- 创建了前端组件 `src/components/MergeNovels.tsx`
- 创建了合并页面 `src/app/merge/page.tsx`
- 在主页面添加了"合并章节"链接

### 新增文件
- `src/lib/file-manager.ts` (修改)
- `src/app/api/merge/route.ts` (新增)
- `src/components/MergeNovels.tsx` (新增)
- `src/app/merge/page.tsx` (新增)
- `src/app/page.tsx` (修改)

## 功能特点

### 1. 确定性ID生成
- 基于书名的MD5哈希生成18位ID
- 相同书名总是生成相同ID
- 重新解析时保持人物等数据的关联

### 2. 改进的错误处理
- 更严格的null值检查
- 正确的任务状态管理
- 详细的错误信息记录

### 3. 章节合并功能
- 自动按章节号排序
- 生成带时间戳的合并文件
- 保存到独立的 `done-novels` 目录
- 友好的用户界面

## 使用方法

1. **重新解析小说**: 现在重新解析相同书名的小说会保持相同的ID
2. **改写任务**: 任务完成后会正确显示为"已完成"状态
3. **合并章节**: 
   - 访问 `/merge` 页面
   - 选择要合并的小说
   - 点击"合并章节"按钮
   - 合并后的文件会保存在 `done-novels` 目录

## 技术细节

- 使用 Node.js crypto 模块生成确定性哈希
- 改进了异步错误处理
- 添加了文件系统操作的错误处理
- 使用 TypeScript 确保类型安全