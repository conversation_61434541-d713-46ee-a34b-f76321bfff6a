{"/api/chapters/route": "app/api/chapters/route.js", "/api/characters/route": "app/api/characters/route.js", "/api/gemini/stats/route": "app/api/gemini/stats/route.js", "/api/jobs/route": "app/api/jobs/route.js", "/api/merge/route": "app/api/merge/route.js", "/api/novels/route": "app/api/novels/route.js", "/api/presets/route": "app/api/presets/route.js", "/api/rewrite/route": "app/api/rewrite/route.js", "/merge/page": "app/merge/page.js", "/page": "app/page.js"}